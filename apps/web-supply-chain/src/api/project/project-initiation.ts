import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface ProjectPageParams extends BaseDataParams {
  // 项目编码
  projectCode: string;
  // 项目名称
  projectName: string;
  // 业务结构(先采后销, 先销后采)
  businessStructure: string;
  // 项目模式(建材模式, 产业模式)
  projectModel: string;
  // 采购模式(预付款, 货到付款)
  purchaseMode: string;
  // 当前页
  current?: string;
  // 每页的数量
  size?: string;
  // 正排序规则
  ascs?: string;
  // 倒排序规则
  descs?: string;
  // 评审节点类型
  reviewNodeType?: string;
  // 支委会节点类型
  partyBranchNodeType?: string;
  // 总经办节点类型
  generalManageNodeType?: string;
  // 主键
  id?: number;
  // 创建时间
  createTime?: string;
  // 创建人ID
  createBy?: string;
  // 修改时间
  updateTime?: string;
  // 修改人ID
  updateBy?: string;
  // 版本号
  version?: string;
  // 贸易执行企业编码
  executorCompanyCode?: string;
  // 贸易执行企业名称
  executorCompanyName?: string;
  // 业务负责人ID
  businessManagerId?: string;
  // 业务负责人名称
  businessManagerName?: string;
  // 是否货权管控模式 (1:是, 0:否)
  isGoodsControlMode?: string;
  // 账期天数（天)
  paymentTermDays?: string;
  // 业务日期起
  businessStartDate?: string;
  // 业务日期止
  businessEndDate?: string;
  // 预计开始日期
  planStartDate?: string;
  // 预计结束日期
  estimatedEndDate?: string;
  // 授信到期日
  creditDueDate?: string;
  // 预期项目规模(元)
  expectedProjectScale?: string;
  // 服务费率（年/%)
  serviceFeeRate?: string;
  // 项目地点
  projectAddress?: string;
  // 项目备注
  remarks?: string;
  // 是否有保证金 (1:是, 0:否)
  isDeposit?: string;
  // 抵押信息描述
  mortgageInfoDesc?: string;
  // 质押信息描述
  pledgeInfoDesc?: string;
  // 担保信息描述
  guaranteeInfoDesc?: string;
  // 风险控制描述
  riskControlDesc?: string;
  // 操作状态
  status?: string;
  // 审批状态
  approvalStatus?: string;
  // 上游企业
  suppliers?: string;
  // 下游企业
  purchasers?: string;
}

// 定义正确的类型
export interface ProjectItem {
  id: number;
  projectCode: string;
  projectName: string;
  businessStructure: string;
  projectModel: string;
  supplierCompanyName: string;
  purchaserCompanyName: string;
  executorCompanyName: string;
  partyBranchMeetingStatus: string;
  approvalStatus: string;
  businessManagerName: string;
  planStartDate: string;
  createTime: string;
  createBy: string;
  createDeptName: string;
  projectType: string;
}

export interface ProjectBaseInfo extends BaseDataParams {
  // 主键
  id: number;
  // 项目 ID
  projectId?: number;
  // 创建时间
  createTime: string;
  // 创建人ID
  createBy: number;
  // 修改时间
  updateTime: string;
  // 修改人ID
  updateBy: number;
  // 标记删除
  // deleteFlag?: boolean;
  // 版本号
  version: number;
  // 项目编码
  projectCode: string;
  // 项目名称
  projectName: string;
  // 贸易执行企业编码
  executorCompanyCode?: string;
  // 贸易执行企业名称
  executorCompanyName?: string;
  // 业务结构(先采后销, 先销后采)
  businessStructure: string;
  // 项目模式(建材模式, 产业模式)
  projectModel: string;
  // 采购模式(预付款, 货到付款)
  purchaseMode: string[];
  // 是否货权管控模式 (1:是, 0:否)
  isGoodsControlMode?: number;
  // 账期天数（天)
  paymentTermDays?: number;
  // 预计开始日期
  planStartDate?: string;
  // 授信到期日期
  creditDueDate?: string;
  // 预期项目规模(元)
  expectedProjectScale?: number;
  // 服务费率（年/%)
  serviceFeeRate?: number;
  // 省份
  province?: string;
  // 城市
  city?: string;
  // 区县
  district?: string;
  // 详细地址
  detailAddress?: string;
  // 项目备注
  remarks?: string;
  // 是否有保证金 (1:是, 0:否)
  isDeposit?: number;
  // 抵押附件
  mortgageInfoAttachment: number;
  // 抵押信息描述
  mortgageInfoDesc?: string;
  // 质押附件
  pledgeInfoAttachment: number;
  // 质押信息描述
  pledgeInfoDesc?: string;
  // 担保信息描述
  guaranteeInfoDesc?: string;
  // 风险控制描述
  riskControlDesc?: string;
  // 增信描述
  creditEnhancementDesc?: string;
  // 操作状态
  status?: string;
  // 审批状态
  approvalStatus?: string;
  // 付款方式
  paymentMethod?: string[];
  // 回款方式
  collectionMethod?: string[];
  // 结算方式
  settlementMethod?: string;
  // 是否支持重点产业链 (1:是, 0:否)
  isKeyIndustry?: number;
  // 是否支持实体企业 (1:是, 0:否)
  isRealEnterprise?: number;
  // 业务负责人ID
  businessManagerId?: number[];
  // 运营负责人ID
  operationManagerId?: number[];
  // 财务负责人ID
  financeManagerId?: number[];
  // 风控负责人ID
  riskManagerId?: number[];
  // 业务负责人list
  businessManager?: managersObj[];
  // 运营负责人list
  operationManager?: managersObj[];
  //财务负责人list
  financeManager?: managersObj[];
  // 风控负责人list
  riskManager?: managersObj[];
  // 担保企业编码
  guaranteeCompanyCode?: string;
  // 担保企业名称
  guaranteeCompanyName?: string;
  // 授信类型 (1: 循环, 2: 单次)
  creditType?: string;
  projectReviewId?: number;
  partyBranchId?: number;
  generalManagerId?: number;
  // 项目合作方关联
  projectPartners?: ProjectPartners[];
  reviewNodeId?: number;
  // 业务附件
  attachmentList?: AttachmentList[];
  // // 评审会议纪要
  // reviewRecordList?: AttachmentList[];
  // // 支委会上会资料
  // branchCommitteeList: AttachmentList[];
  // // 支委会会议纪要
  // branchRecordList?: AttachmentList[];
  // // 总经办上会资料
  // generalManagerList?: AttachmentList[];
  // // 总经办会议纪要
  // managerRecordList?: AttachmentList[];
  deleteProjectPartners: ProjectPartners[];
  // 区分项目类型
  projectType: string;
}

export interface managersObj {
  // 主键
  id: number;
  // 业务ID (例如: 项目ID, 订单ID等)
  businessId: number;
  // 业务类型 (例如: PROJECT)
  businessType: string;
  // 参与角色 (例如: 业务负责人, 运营人员, 财务人员, 风控人员)
  participantRole: string;
  // 用户ID
  userId: number;
  // 用户名称 (为方便查询而冗余的字段)
  userName: string;
}

export interface ProjectPartners {
  // 主键
  id?: number;
  // 版本号
  version?: number;
  // 关联的项目ID
  projectId?: number;
  // 合作方类型
  partnerType?: string;
  // 企业编码
  companyCode?: string;
  // 企业名称
  companyName?: string;
  // 项目下企业额度上限（元）
  subLimitAmount?: number;
  // 是否占用企业总额度（元）
  occupyLimit?: number;
  // 授信类型 (1: 循环, 2: 单次)
  creditType?: string;
  // 额度到期日
  expiryDate?: string;
}

export interface AttachmentList {
  // 主键
  id: number | undefined;
}

export interface MeetingInfo {
  reviewNodeId?: number;
  attachmentList?: number[];
}

// 项目分页查询
export async function projectProposalPageApi(params: PageListParams) {
  return requestClient.get<ProjectPageParams[]>('/scm/project/proposal/page', { params });
}

// 创建项目信息
export async function projectProposalAddApi(data: ProjectBaseInfo) {
  return requestClient.post<ProjectBaseInfo>('/scm/project/proposal/add', data);
}

// 编辑项目信息
export async function projectProposalEditApi(data: ProjectBaseInfo) {
  return requestClient.post<ProjectBaseInfo>('/scm/project/proposal/edit', data);
}

// 项目详情查看
export async function projectProposalDetailApi(id: number) {
  return requestClient.get<ProjectBaseInfo>(`/scm/project/proposal/detail/${id}`);
}

// 项目信息删除
export async function projectProposalDeleteApi(id: number) {
  return requestClient.post(`/scm/project/proposal/delete/${id}`);
}

// 项目信息作废
export async function projectProposalCancelApi(id: number) {
  return requestClient.post(`/scm/project/proposal/cancel/${id}`);
}

// 项目信息提交
export async function projectProposalSubmitApi(data: ProjectBaseInfo) {
  return requestClient.post('/scm/project/proposal/submit', data);
}

// 项目条件查询
export async function projectProposalListApi(params?: PageListParams) {
  return requestClient.get<ProjectPageParams[]>('/scm/project/proposal/list', { params });
}

// 查询企业列表
export async function getCompanyApi() {
  return requestClient.get('/base/company/list');
}

// 查询用户列表
export async function getUserListApi() {
  return requestClient.get('/upms/user/list');
}
