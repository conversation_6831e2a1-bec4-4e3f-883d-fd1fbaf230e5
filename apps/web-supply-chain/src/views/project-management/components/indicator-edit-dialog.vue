<script setup lang="ts">
import type { IndicatorListInfo } from '#/api';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Table } from 'ant-design-vue';

import { getIndicatorConfigListApi } from '#/api';

const config = ref<IndicatorListInfo>({});
const tableData = ref<IndicatorListInfo['categoryIndicators']>([]);
const columns = [
  { title: '指标分类', dataIndex: 'categoryName', key: 'categoryName' },
  { title: '指标名称', dataIndex: 'indicatorName', key: 'indicatorName' },
  { title: '权重', dataIndex: 'weight', key: 'weight' },
  { title: '打分标准', dataIndex: 'detail', key: 'detail' },
  { title: '打分结果', dataIndex: 'result', key: 'result' },
];
const [Modal, modalApi] = useVbenModal();
const getIndicatorConfig = async () => {
  config.value = await getIndicatorConfigListApi();
  const data = config.value.categoryIndicators ?? [];
  data.push({
    indicatorName: '总分',
  });
  tableData.value = data;
};
const init = () => {
  getIndicatorConfig();
  modalApi.open();
};
defineExpose({
  init,
  modalApi,
});
</script>

<template>
  <Modal title="额度测算" class="w-[1200px]">
    <Table :data-source="tableData" :columns="columns" :pagination="false" />
  </Modal>
</template>

<style></style>
